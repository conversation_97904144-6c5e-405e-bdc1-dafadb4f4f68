import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server'
import { NextResponse } from 'next/server'
import { getUserIdFromHeaders, getUserRoleFromHeaders } from './lib/auth'

// Define role-based access control
const rolePermissions = {
  'super-admin': ['*'], // Access to everything
  'hr-admin': [
    '/dashboard',
    '/dashboard/employees',
    '/dashboard/departments', 
    '/dashboard/periods',
    '/dashboard/approvals',
    '/dashboard/appraisal'
  ],
  'manager': [
    '/dashboard',
    '/dashboard/appraisal'
  ],
  'accountant': [
    '/dashboard',
    '/dashboard/approvals'
  ]
} as const

type UserRole = keyof typeof rolePermissions

function hasAccess(userRole: UserRole, pathname: string): boolean {
  const permissions = rolePermissions[userRole]
  
  // Super admin has access to everything
  if ((permissions as readonly string[]).includes('*')) {
    return true
  }
  
  // Check if the user has explicit permission for this path
  return permissions.some(permission => {
    if (permission === pathname) return true
    // Allow access to sub-paths (e.g., /dashboard/appraisal/123)
    if (pathname.startsWith(permission + '/')) return true
    return false
  })
}

function getUserRole(auth: any): UserRole {
  // In a real implementation, this would come from the user's metadata in Clerk
  // For now, we'll use a default role or extract from user metadata
  const role = auth.sessionClaims?.metadata?.role || 'manager'
  
  // Validate the role
  if (role in rolePermissions) {
    return role as UserRole
  }
  
  // Default to manager if role is invalid
  return 'manager'
}

// Define protected routes
const isProtectedRoute = createRouteMatcher([
  '/dashboard(.*)',
])

export default clerkMiddleware(async (auth, req) => {
  const { pathname } = req.nextUrl

  // Protect routes that require authentication
  if (isProtectedRoute(req)) {
    await auth()
  }

  // Get user info for protected routes
  const { userId } = await auth()

  // Redirect authenticated users away from auth pages
  if (userId && (pathname === '/sign-in' || pathname === '/sign-up')) {
    return NextResponse.redirect(new URL('/dashboard', req.url))
  }

  // Get auth data for role checking
  const authData = await auth()

  // For protected routes, check role-based permissions
  if (isProtectedRoute(req) && userId) {
    const userRole = getUserRole(authData)

    // Check if user has access to this route
    if (!hasAccess(userRole, pathname)) {
      console.warn(`Access denied for user ${userId} with role ${userRole} to ${pathname}`)

      // Redirect to dashboard with error message
      const dashboardUrl = new URL('/dashboard', req.url)
      dashboardUrl.searchParams.set('error', 'access_denied')
      return NextResponse.redirect(dashboardUrl)
    }
  }

  // Add user info to headers for server components
  const requestHeaders = new Headers(req.headers)
  if (userId) {
    const userRole = getUserRole(authData)
    requestHeaders.set('x-user-id', userId)
    requestHeaders.set('x-user-role', userRole)
  }

  // Add security headers
  const response = NextResponse.next({
    request: {
      headers: requestHeaders,
    },
  })

  // Security headers
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  response.headers.set('X-XSS-Protection', '1; mode=block')

  // CSP header for additional security
  const cspHeader = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-eval' 'unsafe-inline' https://clerk.com",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "font-src 'self' data:",
    "connect-src 'self' https://clerk.com https://*.clerk.accounts.dev",
    "frame-src 'self' https://clerk.com",
  ].join('; ')

  response.headers.set('Content-Security-Policy', cspHeader)

  return response
})

export const config = {
  matcher: [
    // Match all routes except static files and API routes that should be ignored
    '/((?!.*\\..*|_next).*)',
    '/',
    '/(api|trpc)(.*)'
  ],
}

// Export helper functions for use in server components
export { getUserRoleFromHeaders, getUserIdFromHeaders, type UserRole }
