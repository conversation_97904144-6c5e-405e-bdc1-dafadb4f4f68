/**
 * Security utilities and helpers
 */

import { log } from '@/lib/logging'
import crypto from 'crypto'

// Input sanitization
export function sanitizeInput(input: string): string {
  if (typeof input !== 'string') {
    return ''
  }
  
  // Remove potentially dangerous characters
  return input
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .replace(/[<>]/g, '') // Remove angle brackets
    .trim()
}

// HTML encoding for safe display
export function encodeHTML(str: string): string {
  const div = document.createElement('div')
  div.textContent = str
  return div.innerHTML
}

// SQL injection prevention (for raw queries)
export function escapeSQLString(str: string): string {
  return str.replace(/'/g, "''").replace(/\\/g, '\\\\')
}

// CSRF token generation and validation
export class CSRFProtection {
  private static readonly TOKEN_LENGTH = 32
  private static tokens: Map<string, { token: string; expires: number }> = new Map()

  static generateToken(sessionId: string): string {
    const token = crypto.randomBytes(this.TOKEN_LENGTH).toString('hex')
    const expires = Date.now() + (60 * 60 * 1000) // 1 hour
    
    this.tokens.set(sessionId, { token, expires })
    
    log.debug('CSRF token generated', { sessionId }, 'Security')
    return token
  }

  static validateToken(sessionId: string, token: string): boolean {
    const stored = this.tokens.get(sessionId)
    
    if (!stored) {
      log.security('CSRF token validation failed - no token found', { sessionId })
      return false
    }
    
    if (stored.expires < Date.now()) {
      this.tokens.delete(sessionId)
      log.security('CSRF token validation failed - token expired', { sessionId })
      return false
    }
    
    if (stored.token !== token) {
      log.security('CSRF token validation failed - token mismatch', { sessionId })
      return false
    }
    
    log.debug('CSRF token validated successfully', { sessionId }, 'Security')
    return true
  }

  static cleanupExpiredTokens() {
    const now = Date.now()
    let cleaned = 0
    
    for (const [sessionId, data] of this.tokens.entries()) {
      if (data.expires < now) {
        this.tokens.delete(sessionId)
        cleaned++
      }
    }
    
    if (cleaned > 0) {
      log.debug(`Cleaned up ${cleaned} expired CSRF tokens`, {}, 'Security')
    }
  }
}

// Password strength validation
export interface PasswordStrength {
  score: number // 0-4
  feedback: string[]
  isValid: boolean
}

export function validatePasswordStrength(password: string): PasswordStrength {
  const feedback: string[] = []
  let score = 0

  // Length check
  if (password.length >= 8) {
    score++
  } else {
    feedback.push('Password must be at least 8 characters long')
  }

  // Uppercase check
  if (/[A-Z]/.test(password)) {
    score++
  } else {
    feedback.push('Password must contain at least one uppercase letter')
  }

  // Lowercase check
  if (/[a-z]/.test(password)) {
    score++
  } else {
    feedback.push('Password must contain at least one lowercase letter')
  }

  // Number check
  if (/\d/.test(password)) {
    score++
  } else {
    feedback.push('Password must contain at least one number')
  }

  // Special character check
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    score++
  } else {
    feedback.push('Password must contain at least one special character')
  }

  // Common password check
  const commonPasswords = [
    'password', '123456', 'password123', 'admin', 'qwerty',
    'letmein', 'welcome', 'monkey', '1234567890'
  ]
  
  if (commonPasswords.includes(password.toLowerCase())) {
    score = Math.max(0, score - 2)
    feedback.push('Password is too common')
  }

  return {
    score: Math.min(score, 4),
    feedback,
    isValid: score >= 3 && feedback.length === 0
  }
}

// Secure random string generation
export function generateSecureRandomString(length: number = 32): string {
  return crypto.randomBytes(length).toString('hex')
}

// Hash generation for sensitive data
export function generateHash(data: string, salt?: string): string {
  const actualSalt = salt || crypto.randomBytes(16).toString('hex')
  const hash = crypto.pbkdf2Sync(data, actualSalt, 10000, 64, 'sha512')
  return `${actualSalt}:${hash.toString('hex')}`
}

// Hash verification
export function verifyHash(data: string, hash: string): boolean {
  const [salt, originalHash] = hash.split(':')
  const newHash = crypto.pbkdf2Sync(data, salt, 10000, 64, 'sha512')
  return originalHash === newHash.toString('hex')
}

// Content Security Policy helpers
export function generateCSPNonce(): string {
  return crypto.randomBytes(16).toString('base64')
}

export function buildCSPHeader(nonce?: string): string {
  const directives = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline'" + (nonce ? ` 'nonce-${nonce}'` : ''),
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "font-src 'self'",
    "connect-src 'self'",
    "frame-ancestors 'none'",
    "base-uri 'self'",
    "form-action 'self'"
  ]
  
  return directives.join('; ')
}

// Request validation
export interface RequestValidationResult {
  isValid: boolean
  errors: string[]
  sanitizedData?: any
}

export function validateRequest(
  data: any,
  rules: Record<string, (value: any) => boolean | string>
): RequestValidationResult {
  const errors: string[] = []
  const sanitizedData: any = {}

  for (const [field, validator] of Object.entries(rules)) {
    const value = data[field]
    const result = validator(value)
    
    if (typeof result === 'string') {
      errors.push(`${field}: ${result}`)
    } else if (!result) {
      errors.push(`${field}: validation failed`)
    } else {
      // Sanitize the value if validation passed
      sanitizedData[field] = typeof value === 'string' ? sanitizeInput(value) : value
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    sanitizedData: errors.length === 0 ? sanitizedData : undefined
  }
}

// Security headers
export function getSecurityHeaders(): Record<string, string> {
  return {
    'X-Frame-Options': 'DENY',
    'X-Content-Type-Options': 'nosniff',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
    'Content-Security-Policy': buildCSPHeader()
  }
}

// Audit logging for security events
export function auditLog(
  event: string,
  userId?: string,
  details?: Record<string, any>,
  severity: 'low' | 'medium' | 'high' | 'critical' = 'medium'
) {
  const auditData = {
    event,
    userId,
    timestamp: new Date().toISOString(),
    severity,
    details,
    userAgent: typeof window !== 'undefined' ? navigator.userAgent : undefined,
    ip: 'server-side' // Would be populated by middleware
  }

  // Log based on severity
  switch (severity) {
    case 'critical':
      log.fatal(`Security audit: ${event}`, undefined, auditData, 'Security')
      break
    case 'high':
      log.error(`Security audit: ${event}`, undefined, auditData, 'Security')
      break
    case 'medium':
      log.warn(`Security audit: ${event}`, auditData, 'Security')
      break
    case 'low':
      log.info(`Security audit: ${event}`, auditData, 'Security')
      break
  }

  // In production, this would also send to a security monitoring service
  if (process.env.NODE_ENV === 'production' && severity === 'critical') {
    // Send alert to security team
    console.error('CRITICAL SECURITY EVENT:', auditData)
  }
}

// Session security
export function validateSession(sessionData: any): boolean {
  if (!sessionData) {
    auditLog('Invalid session - no data', undefined, {}, 'high')
    return false
  }

  if (!sessionData.userId || !sessionData.expires) {
    auditLog('Invalid session - missing required fields', sessionData.userId, sessionData, 'high')
    return false
  }

  if (new Date(sessionData.expires) < new Date()) {
    auditLog('Session expired', sessionData.userId, { expires: sessionData.expires }, 'low')
    return false
  }

  return true
}

// Environment-specific security configurations
export function getSecurityConfig() {
  const isDevelopment = process.env.NODE_ENV === 'development'
  const isProduction = process.env.NODE_ENV === 'production'

  return {
    // CSRF protection
    csrfEnabled: isProduction,
    
    // Rate limiting
    rateLimitEnabled: isProduction,
    
    // Security headers
    securityHeadersEnabled: true,
    
    // Audit logging
    auditLoggingEnabled: true,
    auditLogLevel: isDevelopment ? 'debug' : 'info',
    
    // Session security
    sessionTimeout: isDevelopment ? 24 * 60 * 60 * 1000 : 2 * 60 * 60 * 1000, // 24h dev, 2h prod
    
    // Password requirements
    passwordMinLength: isDevelopment ? 6 : 8,
    passwordRequireSpecialChars: !isDevelopment,
    
    // HTTPS enforcement
    httpsOnly: isProduction,
    
    // Content Security Policy
    cspEnabled: isProduction,
    cspReportOnly: isDevelopment
  }
}

// Initialize security measures
export function initializeSecurity() {
  log.info('Initializing security measures', getSecurityConfig(), 'Security')
  
  // Set up CSRF token cleanup
  setInterval(() => {
    CSRFProtection.cleanupExpiredTokens()
  }, 60 * 60 * 1000) // Every hour

  auditLog('Security system initialized', undefined, getSecurityConfig(), 'low')
}

console.log('[SECURITY] Security utilities loaded')
