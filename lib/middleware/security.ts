/**
 * Security middleware for API routes
 */

import { NextRequest, NextResponse } from 'next/server'
import { rateLimiters, withRateLimit, getClientIP } from '@/lib/rate-limiting'
import { getSecurityHeaders, auditLog, validateSession } from '@/lib/security'
import { log } from '@/lib/logging'
import { isProduction } from '@/lib/config'

export interface SecurityMiddlewareOptions {
  requireAuth?: boolean
  rateLimiter?: 'api' | 'auth' | 'forms' | 'uploads' | 'passwordReset'
  csrfProtection?: boolean
  corsEnabled?: boolean
  allowedMethods?: string[]
  maxBodySize?: number
}

export function withSecurity(
  handler: (req: NextRequest) => Promise<NextResponse> | NextResponse,
  options: SecurityMiddlewareOptions = {}
) {
  return async function securityMiddleware(req: NextRequest): Promise<NextResponse> {
    const startTime = performance.now()
    const clientIP = getClientIP(req)
    const userAgent = req.headers.get('user-agent') || 'unknown'
    const method = req.method
    const url = req.url

    log.debug('Security middleware processing request', {
      method,
      url,
      clientIP,
      userAgent
    }, 'SecurityMiddleware')

    try {
      // 1. Method validation
      if (options.allowedMethods && !options.allowedMethods.includes(method)) {
        auditLog('Method not allowed', undefined, { method, url, clientIP }, 'medium')
        return new NextResponse('Method Not Allowed', { 
          status: 405,
          headers: {
            'Allow': options.allowedMethods.join(', '),
            ...getSecurityHeaders()
          }
        })
      }

      // 2. Body size validation
      if (options.maxBodySize && req.body) {
        const contentLength = req.headers.get('content-length')
        if (contentLength && parseInt(contentLength) > options.maxBodySize) {
          auditLog('Request body too large', undefined, { 
            contentLength, 
            maxSize: options.maxBodySize,
            clientIP 
          }, 'medium')
          return new NextResponse('Payload Too Large', { 
            status: 413,
            headers: getSecurityHeaders()
          })
        }
      }

      // 3. Rate limiting
      if (options.rateLimiter && isProduction) {
        const limiter = rateLimiters[options.rateLimiter]
        if (limiter) {
          const rateLimitResponse = withRateLimit(limiter)(req)
          if (rateLimitResponse) {
            return rateLimitResponse
          }
        }
      }

      // 4. CORS handling
      if (options.corsEnabled) {
        const origin = req.headers.get('origin')
        const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000']
        
        if (origin && !allowedOrigins.includes(origin)) {
          auditLog('CORS violation', undefined, { origin, allowedOrigins, clientIP }, 'medium')
          return new NextResponse('Forbidden', { 
            status: 403,
            headers: getSecurityHeaders()
          })
        }

        // Handle preflight requests
        if (method === 'OPTIONS') {
          return new NextResponse(null, {
            status: 200,
            headers: {
              'Access-Control-Allow-Origin': origin || '*',
              'Access-Control-Allow-Methods': options.allowedMethods?.join(', ') || 'GET, POST, PUT, DELETE, OPTIONS',
              'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-CSRF-Token',
              'Access-Control-Max-Age': '86400',
              ...getSecurityHeaders()
            }
          })
        }
      }

      // 5. CSRF protection
      if (options.csrfProtection && isProduction && ['POST', 'PUT', 'DELETE', 'PATCH'].includes(method)) {
        const csrfToken = req.headers.get('x-csrf-token')
        const sessionId = req.headers.get('x-session-id') || clientIP
        
        if (!csrfToken) {
          auditLog('Missing CSRF token', undefined, { method, url, clientIP }, 'high')
          return new NextResponse('CSRF token required', { 
            status: 403,
            headers: getSecurityHeaders()
          })
        }

        // In a real implementation, you would validate the CSRF token here
        // For now, we'll just log the attempt
        log.debug('CSRF token validation', { csrfToken, sessionId }, 'SecurityMiddleware')
      }

      // 6. Authentication check
      if (options.requireAuth) {
        const authHeader = req.headers.get('authorization')
        const sessionCookie = req.cookies.get('session')
        
        if (!authHeader && !sessionCookie) {
          auditLog('Unauthorized access attempt', undefined, { url, clientIP }, 'medium')
          return new NextResponse('Unauthorized', { 
            status: 401,
            headers: {
              'WWW-Authenticate': 'Bearer',
              ...getSecurityHeaders()
            }
          })
        }

        // Validate session if present
        if (sessionCookie) {
          try {
            const sessionData = JSON.parse(sessionCookie.value)
            if (!validateSession(sessionData)) {
              auditLog('Invalid session', sessionData.userId, { sessionData }, 'medium')
              return new NextResponse('Invalid session', { 
                status: 401,
                headers: getSecurityHeaders()
              })
            }
          } catch (error) {
            auditLog('Session parsing error', undefined, { error: error.message }, 'medium')
            return new NextResponse('Invalid session format', { 
              status: 401,
              headers: getSecurityHeaders()
            })
          }
        }
      }

      // 7. Content type validation for POST/PUT requests
      if (['POST', 'PUT', 'PATCH'].includes(method)) {
        const contentType = req.headers.get('content-type')
        if (contentType && !contentType.includes('application/json') && !contentType.includes('multipart/form-data')) {
          auditLog('Invalid content type', undefined, { contentType, method, url }, 'low')
          return new NextResponse('Unsupported Media Type', { 
            status: 415,
            headers: getSecurityHeaders()
          })
        }
      }

      // 8. Execute the main handler
      const response = await handler(req)

      // 9. Add security headers to response
      const securityHeaders = getSecurityHeaders()
      Object.entries(securityHeaders).forEach(([key, value]) => {
        response.headers.set(key, value)
      })

      // 10. Add CORS headers if enabled
      if (options.corsEnabled) {
        const origin = req.headers.get('origin')
        if (origin) {
          response.headers.set('Access-Control-Allow-Origin', origin)
          response.headers.set('Access-Control-Allow-Credentials', 'true')
        }
      }

      // 11. Add rate limit headers
      if (options.rateLimiter && isProduction) {
        const limiter = rateLimiters[options.rateLimiter]
        if (limiter) {
          const status = limiter.getStatus(clientIP)
          response.headers.set('X-RateLimit-Limit', limiter['config'].maxRequests.toString())
          response.headers.set('X-RateLimit-Remaining', status.remaining.toString())
          response.headers.set('X-RateLimit-Reset', new Date(status.resetTime).toISOString())
        }
      }

      // 12. Log successful request
      const duration = performance.now() - startTime
      log.info('Request processed successfully', {
        method,
        url,
        status: response.status,
        duration: `${duration.toFixed(2)}ms`,
        clientIP
      }, 'SecurityMiddleware')

      return response

    } catch (error) {
      const duration = performance.now() - startTime
      
      log.error('Security middleware error', error as Error, {
        method,
        url,
        duration: `${duration.toFixed(2)}ms`,
        clientIP
      }, 'SecurityMiddleware')

      auditLog('Security middleware error', undefined, {
        error: error.message,
        method,
        url,
        clientIP
      }, 'high')

      return new NextResponse('Internal Server Error', { 
        status: 500,
        headers: getSecurityHeaders()
      })
    }
  }
}

// Predefined security configurations for common use cases
export const securityConfigs = {
  // Public API endpoints
  public: {
    requireAuth: false,
    rateLimiter: 'api' as const,
    corsEnabled: true,
    allowedMethods: ['GET', 'POST', 'OPTIONS'],
    maxBodySize: 1024 * 1024 // 1MB
  },

  // Protected API endpoints
  protected: {
    requireAuth: true,
    rateLimiter: 'api' as const,
    csrfProtection: true,
    corsEnabled: true,
    allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    maxBodySize: 5 * 1024 * 1024 // 5MB
  },

  // Authentication endpoints
  auth: {
    requireAuth: false,
    rateLimiter: 'auth' as const,
    csrfProtection: true,
    corsEnabled: true,
    allowedMethods: ['POST', 'OPTIONS'],
    maxBodySize: 1024 // 1KB
  },

  // Form submission endpoints
  forms: {
    requireAuth: true,
    rateLimiter: 'forms' as const,
    csrfProtection: true,
    corsEnabled: true,
    allowedMethods: ['POST', 'PUT', 'OPTIONS'],
    maxBodySize: 10 * 1024 * 1024 // 10MB
  },

  // File upload endpoints
  uploads: {
    requireAuth: true,
    rateLimiter: 'uploads' as const,
    csrfProtection: true,
    corsEnabled: true,
    allowedMethods: ['POST', 'OPTIONS'],
    maxBodySize: 50 * 1024 * 1024 // 50MB
  }
}

console.log('[SECURITY] Security middleware loaded')
