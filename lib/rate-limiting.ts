/**
 * Rate limiting utilities for API protection
 */

import { log } from '@/lib/logging'

interface RateLimitConfig {
  windowMs: number // Time window in milliseconds
  maxRequests: number // Maximum requests per window
  skipSuccessfulRequests?: boolean
  skipFailedRequests?: boolean
  keyGenerator?: (identifier: string) => string
}

interface RateLimitEntry {
  count: number
  resetTime: number
  firstRequest: number
}

class RateLimiter {
  private store: Map<string, RateLimitEntry> = new Map()
  private config: RateLimitConfig
  private cleanupInterval: NodeJS.Timeout | null = null

  constructor(config: RateLimitConfig) {
    this.config = {
      skipSuccessfulRequests: false,
      skipFailedRequests: false,
      keyGenerator: (id) => id,
      ...config
    }

    // Clean up expired entries every minute
    this.cleanupInterval = setInterval(() => {
      this.cleanup()
    }, 60000)

    log.info('Rate limiter initialized', { config: this.config }, 'RateLimiter')
  }

  private cleanup() {
    const now = Date.now()
    let cleaned = 0

    for (const [key, entry] of this.store.entries()) {
      if (entry.resetTime <= now) {
        this.store.delete(key)
        cleaned++
      }
    }

    if (cleaned > 0) {
      log.debug(`Cleaned up ${cleaned} expired rate limit entries`, {}, 'RateLimiter')
    }
  }

  private getKey(identifier: string): string {
    return this.config.keyGenerator!(identifier)
  }

  check(identifier: string): { allowed: boolean; remaining: number; resetTime: number; retryAfter?: number } {
    const key = this.getKey(identifier)
    const now = Date.now()
    
    let entry = this.store.get(key)

    // Create new entry if doesn't exist or window has expired
    if (!entry || entry.resetTime <= now) {
      entry = {
        count: 0,
        resetTime: now + this.config.windowMs,
        firstRequest: now
      }
      this.store.set(key, entry)
    }

    // Check if limit exceeded
    if (entry.count >= this.config.maxRequests) {
      const retryAfter = Math.ceil((entry.resetTime - now) / 1000)
      
      log.warn('Rate limit exceeded', {
        identifier,
        count: entry.count,
        limit: this.config.maxRequests,
        retryAfter
      }, 'RateLimiter')

      return {
        allowed: false,
        remaining: 0,
        resetTime: entry.resetTime,
        retryAfter
      }
    }

    // Increment counter
    entry.count++
    this.store.set(key, entry)

    const remaining = this.config.maxRequests - entry.count

    log.debug('Rate limit check passed', {
      identifier,
      count: entry.count,
      remaining,
      limit: this.config.maxRequests
    }, 'RateLimiter')

    return {
      allowed: true,
      remaining,
      resetTime: entry.resetTime
    }
  }

  // Record a request (for conditional counting)
  record(identifier: string, success: boolean = true) {
    if (this.config.skipSuccessfulRequests && success) {
      return
    }
    if (this.config.skipFailedRequests && !success) {
      return
    }

    // This will increment the counter
    this.check(identifier)
  }

  // Reset rate limit for a specific identifier
  reset(identifier: string) {
    const key = this.getKey(identifier)
    this.store.delete(key)
    log.info('Rate limit reset', { identifier }, 'RateLimiter')
  }

  // Get current status without incrementing
  getStatus(identifier: string): { count: number; remaining: number; resetTime: number } {
    const key = this.getKey(identifier)
    const now = Date.now()
    const entry = this.store.get(key)

    if (!entry || entry.resetTime <= now) {
      return {
        count: 0,
        remaining: this.config.maxRequests,
        resetTime: now + this.config.windowMs
      }
    }

    return {
      count: entry.count,
      remaining: this.config.maxRequests - entry.count,
      resetTime: entry.resetTime
    }
  }

  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }
    this.store.clear()
    log.info('Rate limiter destroyed', {}, 'RateLimiter')
  }
}

// Pre-configured rate limiters for different use cases
export const rateLimiters = {
  // General API rate limiting
  api: new RateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100,
    keyGenerator: (ip) => `api:${ip}`
  }),

  // Authentication attempts
  auth: new RateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5,
    skipSuccessfulRequests: true,
    keyGenerator: (identifier) => `auth:${identifier}`
  }),

  // Form submissions
  forms: new RateLimiter({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10,
    keyGenerator: (identifier) => `form:${identifier}`
  }),

  // File uploads
  uploads: new RateLimiter({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 5,
    keyGenerator: (identifier) => `upload:${identifier}`
  }),

  // Password reset attempts
  passwordReset: new RateLimiter({
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 3,
    keyGenerator: (email) => `pwd-reset:${email}`
  })
}

// Middleware helper for Next.js API routes
export function withRateLimit(
  limiter: RateLimiter,
  getIdentifier: (req: Request) => string = (req) => getClientIP(req)
) {
  return function rateLimitMiddleware(req: Request): Response | null {
    const identifier = getIdentifier(req)
    const result = limiter.check(identifier)

    if (!result.allowed) {
      log.security('Rate limit exceeded', {
        identifier,
        userAgent: req.headers.get('user-agent'),
        url: req.url
      })

      return new Response(
        JSON.stringify({
          error: 'Too many requests',
          message: 'Rate limit exceeded. Please try again later.',
          retryAfter: result.retryAfter
        }),
        {
          status: 429,
          headers: {
            'Content-Type': 'application/json',
            'Retry-After': result.retryAfter?.toString() || '60',
            'X-RateLimit-Limit': limiter['config'].maxRequests.toString(),
            'X-RateLimit-Remaining': result.remaining.toString(),
            'X-RateLimit-Reset': new Date(result.resetTime).toISOString()
          }
        }
      )
    }

    // Add rate limit headers to successful responses
    return null // Continue processing
  }
}

// Get client IP address from request
export function getClientIP(req: Request): string {
  // Check various headers for the real IP
  const forwarded = req.headers.get('x-forwarded-for')
  const realIP = req.headers.get('x-real-ip')
  const cfConnectingIP = req.headers.get('cf-connecting-ip')
  
  if (forwarded) {
    // x-forwarded-for can contain multiple IPs, take the first one
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  if (cfConnectingIP) {
    return cfConnectingIP
  }
  
  // Fallback to a default if no IP found
  return 'unknown'
}

// Rate limiting for specific user actions
export function checkUserActionRateLimit(
  userId: string,
  action: string,
  customLimiter?: RateLimiter
): { allowed: boolean; remaining: number; retryAfter?: number } {
  const limiter = customLimiter || rateLimiters.forms
  const identifier = `user:${userId}:${action}`
  
  const result = limiter.check(identifier)
  
  if (!result.allowed) {
    log.security('User action rate limit exceeded', {
      userId,
      action,
      retryAfter: result.retryAfter
    })
  }
  
  return result
}

// Cleanup function
export function cleanupRateLimiters() {
  Object.values(rateLimiters).forEach(limiter => {
    limiter.destroy()
  })
  log.info('All rate limiters cleaned up', {}, 'RateLimiter')
}

console.log('[SECURITY] Rate limiting utilities loaded')
