import { useEffect, useRef, useCallback } from 'react'
import { log } from '@/lib/logging'

/**
 * Hook for preventing memory leaks in React components
 */
export function useMemoryLeakPrevention(componentName: string) {
  const isMountedRef = useRef(true)
  const timersRef = useRef<Set<NodeJS.Timeout>>(new Set())
  const intervalsRef = useRef<Set<NodeJS.Timeout>>(new Set())
  const listenersRef = useRef<Array<{ element: EventTarget; event: string; handler: EventListener }>>(new Set())
  const observersRef = useRef<Set<IntersectionObserver | MutationObserver | ResizeObserver>>(new Set())
  const abortControllersRef = useRef<Set<AbortController>>(new Set())

  useEffect(() => {
    log.debug(`Component mounted: ${componentName}`, {}, 'MemoryLeakPrevention')
    
    return () => {
      log.debug(`Component unmounting: ${componentName}`, {}, 'MemoryLeakPrevention')
      isMountedRef.current = false
      
      // Clear all timers
      timersRef.current.forEach(timer => {
        clearTimeout(timer)
        log.debug(`Cleared timeout for ${componentName}`, {}, 'MemoryLeakPrevention')
      })
      timersRef.current.clear()
      
      // Clear all intervals
      intervalsRef.current.forEach(interval => {
        clearInterval(interval)
        log.debug(`Cleared interval for ${componentName}`, {}, 'MemoryLeakPrevention')
      })
      intervalsRef.current.clear()
      
      // Remove all event listeners
      listenersRef.current.forEach(({ element, event, handler }) => {
        element.removeEventListener(event, handler)
        log.debug(`Removed event listener ${event} for ${componentName}`, {}, 'MemoryLeakPrevention')
      })
      listenersRef.current.clear()
      
      // Disconnect all observers
      observersRef.current.forEach(observer => {
        observer.disconnect()
        log.debug(`Disconnected observer for ${componentName}`, {}, 'MemoryLeakPrevention')
      })
      observersRef.current.clear()
      
      // Abort all ongoing requests
      abortControllersRef.current.forEach(controller => {
        controller.abort()
        log.debug(`Aborted request for ${componentName}`, {}, 'MemoryLeakPrevention')
      })
      abortControllersRef.current.clear()
    }
  }, [componentName])

  // Safe setTimeout that auto-cleans on unmount
  const safeSetTimeout = useCallback((callback: () => void, delay: number) => {
    if (!isMountedRef.current) {
      log.warn(`Attempted to set timeout on unmounted component: ${componentName}`, {}, 'MemoryLeakPrevention')
      return
    }
    
    const timer = setTimeout(() => {
      if (isMountedRef.current) {
        callback()
      }
      timersRef.current.delete(timer)
    }, delay)
    
    timersRef.current.add(timer)
    return timer
  }, [componentName])

  // Safe setInterval that auto-cleans on unmount
  const safeSetInterval = useCallback((callback: () => void, delay: number) => {
    if (!isMountedRef.current) {
      log.warn(`Attempted to set interval on unmounted component: ${componentName}`, {}, 'MemoryLeakPrevention')
      return
    }
    
    const interval = setInterval(() => {
      if (isMountedRef.current) {
        callback()
      } else {
        clearInterval(interval)
        intervalsRef.current.delete(interval)
      }
    }, delay)
    
    intervalsRef.current.add(interval)
    return interval
  }, [componentName])

  // Safe event listener that auto-removes on unmount
  const safeAddEventListener = useCallback((
    element: EventTarget,
    event: string,
    handler: EventListener,
    options?: boolean | AddEventListenerOptions
  ) => {
    if (!isMountedRef.current) {
      log.warn(`Attempted to add event listener on unmounted component: ${componentName}`, {}, 'MemoryLeakPrevention')
      return
    }
    
    element.addEventListener(event, handler, options)
    listenersRef.current.add({ element, event, handler })
    
    log.debug(`Added event listener ${event} for ${componentName}`, {}, 'MemoryLeakPrevention')
  }, [componentName])

  // Safe observer that auto-disconnects on unmount
  const safeCreateObserver = useCallback(<T extends IntersectionObserver | MutationObserver | ResizeObserver>(
    observer: T
  ): T => {
    if (!isMountedRef.current) {
      log.warn(`Attempted to create observer on unmounted component: ${componentName}`, {}, 'MemoryLeakPrevention')
      return observer
    }
    
    observersRef.current.add(observer)
    log.debug(`Created observer for ${componentName}`, {}, 'MemoryLeakPrevention')
    return observer
  }, [componentName])

  // Safe fetch with AbortController
  const safeFetch = useCallback(async (
    input: RequestInfo | URL,
    init?: RequestInit
  ): Promise<Response> => {
    if (!isMountedRef.current) {
      log.warn(`Attempted to fetch on unmounted component: ${componentName}`, {}, 'MemoryLeakPrevention')
      throw new Error('Component unmounted')
    }
    
    const controller = new AbortController()
    abortControllersRef.current.add(controller)
    
    try {
      const response = await fetch(input, {
        ...init,
        signal: controller.signal
      })
      
      abortControllersRef.current.delete(controller)
      return response
    } catch (error) {
      abortControllersRef.current.delete(controller)
      throw error
    }
  }, [componentName])

  // Check if component is still mounted
  const isMounted = useCallback(() => isMountedRef.current, [])

  // Safe state setter that only updates if mounted
  const safeSetState = useCallback(<T>(
    setter: React.Dispatch<React.SetStateAction<T>>,
    value: React.SetStateAction<T>
  ) => {
    if (isMountedRef.current) {
      setter(value)
    } else {
      log.warn(`Attempted to set state on unmounted component: ${componentName}`, {}, 'MemoryLeakPrevention')
    }
  }, [componentName])

  // Manual cleanup function
  const cleanup = useCallback(() => {
    log.debug(`Manual cleanup triggered for ${componentName}`, {}, 'MemoryLeakPrevention')
    
    timersRef.current.forEach(timer => clearTimeout(timer))
    timersRef.current.clear()
    
    intervalsRef.current.forEach(interval => clearInterval(interval))
    intervalsRef.current.clear()
    
    listenersRef.current.forEach(({ element, event, handler }) => {
      element.removeEventListener(event, handler)
    })
    listenersRef.current.clear()
    
    observersRef.current.forEach(observer => observer.disconnect())
    observersRef.current.clear()
    
    abortControllersRef.current.forEach(controller => controller.abort())
    abortControllersRef.current.clear()
  }, [componentName])

  return {
    isMounted,
    safeSetTimeout,
    safeSetInterval,
    safeAddEventListener,
    safeCreateObserver,
    safeFetch,
    safeSetState,
    cleanup
  }
}

/**
 * Hook for monitoring component performance and memory usage
 */
export function usePerformanceMonitoring(componentName: string) {
  const renderCountRef = useRef(0)
  const mountTimeRef = useRef<number>(0)

  useEffect(() => {
    mountTimeRef.current = performance.now()
    log.performance(`${componentName} mount time`, mountTimeRef.current, 'ms')
    
    return () => {
      const unmountTime = performance.now()
      const lifespan = unmountTime - mountTimeRef.current
      log.performance(`${componentName} lifespan`, lifespan, 'ms')
    }
  }, [componentName])

  useEffect(() => {
    renderCountRef.current += 1
    log.debug(`${componentName} render #${renderCountRef.current}`, {}, 'Performance')
    
    if (renderCountRef.current > 50) {
      log.warn(`High render count for ${componentName}: ${renderCountRef.current}`, {}, 'Performance')
    }
  })

  return {
    renderCount: renderCountRef.current,
    mountTime: mountTimeRef.current
  }
}

/**
 * Hook for detecting and preventing common memory leak patterns
 */
export function useMemoryLeakDetection(componentName: string) {
  const previousPropsRef = useRef<any>()
  const previousStateRef = useRef<any>()
  const reRenderCountRef = useRef(0)

  useEffect(() => {
    reRenderCountRef.current += 1
    
    // Detect excessive re-renders
    if (reRenderCountRef.current > 100) {
      log.warn(`Excessive re-renders detected in ${componentName}: ${reRenderCountRef.current}`, {}, 'MemoryLeakDetection')
    }
    
    // Detect potential memory leaks from unchanged props/state causing re-renders
    if (reRenderCountRef.current > 10) {
      log.debug(`Potential optimization opportunity in ${componentName}: ${reRenderCountRef.current} renders`, {}, 'MemoryLeakDetection')
    }
  })

  const trackProps = useCallback((props: any) => {
    if (previousPropsRef.current && JSON.stringify(previousPropsRef.current) === JSON.stringify(props)) {
      log.debug(`${componentName} re-rendered with same props`, {}, 'MemoryLeakDetection')
    }
    previousPropsRef.current = props
  }, [componentName])

  const trackState = useCallback((state: any) => {
    if (previousStateRef.current && JSON.stringify(previousStateRef.current) === JSON.stringify(state)) {
      log.debug(`${componentName} re-rendered with same state`, {}, 'MemoryLeakDetection')
    }
    previousStateRef.current = state
  }, [componentName])

  return {
    reRenderCount: reRenderCountRef.current,
    trackProps,
    trackState
  }
}

console.log('[PERF] Memory leak prevention hooks loaded')
