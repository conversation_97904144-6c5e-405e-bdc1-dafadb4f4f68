"use client"

import { useState, useEffect } from "react"
import { EmployeesTable } from "@/components/employees-table"
import { TableSkeleton } from "@/components/loading-states"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import type { Employee, Department } from "@/lib/types"

interface EmployeesPageWrapperProps {
  initialEmployees: Employee[]
  initialDepartments: Department[]
}

export function EmployeesPageWrapper({ initialEmployees, initialDepartments }: EmployeesPageWrapperProps) {
  const [employees, setEmployees] = useState<Employee[]>(initialEmployees)
  const [departments, setDepartments] = useState<Department[]>(initialDepartments)
  const [isLoading, setIsLoading] = useState(false)
  const [isInitialLoad, setIsInitialLoad] = useState(true)

  // Simulate initial loading state
  useEffect(() => {
    console.log('[A11Y] Employees page wrapper mounted')
    const timer = setTimeout(() => {
      setIsInitialLoad(false)
    }, 100) // Brief delay to show that loading states work

    return () => clearTimeout(timer)
  }, [])

  // Function to refresh data (could be called from child components)
  const refreshData = async () => {
    console.log('[A11Y] Refreshing employee data')
    setIsLoading(true)
    
    try {
      // In a real app, this would fetch fresh data
      // For now, we'll just simulate a loading state
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Data would be updated here
      console.log('[A11Y] Employee data refreshed')
    } catch (error) {
      console.error('[A11Y] Error refreshing employee data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (isInitialLoad) {
    return (
      <div className="p-4 sm:p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold tracking-tight">Employee Management</h1>
          <p className="text-muted-foreground">Add, edit, and manage all company employees.</p>
        </div>
        <Card>
          <CardHeader>
            <CardTitle>All Employees</CardTitle>
            <CardDescription>A list of all employees in the system.</CardDescription>
          </CardHeader>
          <CardContent>
            <TableSkeleton context="employee data" />
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold tracking-tight">Employee Management</h1>
        <p className="text-muted-foreground">Add, edit, and manage all company employees.</p>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>All Employees</CardTitle>
          <CardDescription>A list of all employees in the system.</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <TableSkeleton context="employee data" />
          ) : (
            <EmployeesTable 
              data={employees} 
              departments={departments}
              onRefresh={refreshData}
            />
          )}
        </CardContent>
      </Card>
    </div>
  )
}
